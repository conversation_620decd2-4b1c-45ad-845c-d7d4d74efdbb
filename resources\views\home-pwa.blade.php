@extends('layouts.app')

@section('title', 'Home - PT. Putera Wibowo Borneo')
@section('description', 'PT. Putera Wibowo Borneo - Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri')

@push('vite-assets')
    @vite([
        'resources/css/home/<USER>',
        'resources/js/home/<USER>',
        'resources/js/pwa.js'
    ])
@endpush

@push('inline-styles')
    <style>
        /* Info-box hover effects */
        .info-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Slider styles */
        .slider-wrapper {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            border-radius: 10px;
        }

        .slider-track {
            display: flex;
            transition: transform 0.5s ease;
            height: 100%;
        }

        .slide {
            min-width: 100%;
            height: 100%;
        }

        .slide-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .dot {
            height: 10px;
            width: 10px;
            margin: 0 5px;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            display: inline-block;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s ease;
        }

        .dot.active,
        .dot:hover {
            background-color: white;
        }

        @media (min-width: 1367px) {
            .main-content {
                padding: clamp(1rem, 1.5vw, 2rem) 0;
            }
        }
    </style>
@endpush

@section('content')
    <div class="mt-8">
        <div class="div2" style="height: 50%; margin-left: clamp(1rem, 5vw, 3.5rem); margin-right: clamp(1rem, 5vw, 3.5rem);">
            <div class="row" style="height: 100%">
                <div class="col-4">
                    <div class="row">
                        <div class="col-md-1 m-0 p-0"><img id="iconcuaca" src="{{asset('assets/icon/sun.png')}}"
                                alt="weather" />
                        </div>
                        <div class="col-md-1 ketderajat">
                            <span id="derajat">33°</span>
                            <span id="ketcuaca">Cerah</span>
                        </div>
                    </div>
                    <div id="time" class="time">08:30</div>
                    <div id="date" class="date">Senin, 12 Juli 2025</div>
                    <div id="holiday" class="holiday">Hari pendidikan nasional</div>
                </div>
                <div class="col-8 position-relative">
                    <div class="slider-wrapper overflow-hidden rounded-sm">
                        <div class="slider-track d-flex transition" id="sliderTrack">

                            {{-- loop image --}}
                            <div class="slide flex-fill position-relative">
                                <a href="{{ route('best_product.index') }}">
                                    <img src="{{asset('images/sample5.png')}}" alt="" class="slide-image">
                                </a>
                            </div>
                            {{-- loop image --}}
                        </div>
                    </div>
                    <!-- Bulatan navigasi -->
                    <div class="slider-dots text-center position-absolute w-100" style="bottom: 10px;">
                        <button class="dot" onclick="moveToSlide(0)"></button>
                        <button class="dot" onclick="moveToSlide(1)"></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="div3 mt-4" style="margin-left: clamp(1rem, 5vw, 3.5rem); margin-right: clamp(1rem, 5vw, 3.5rem);">
            <div class="row">
                <div class="col-6">
                    <div class="info-box">
                        <a href="{{ route('invoice.index') }}">
                            <div class="info-content">
                                <div class="info-icon">
                                    <img src="{{ asset('assets/icon/invoice.png') }}" alt="Invoice">
                                </div>
                                <div class="info-text">
                                    <h3>Invoice</h3>
                                    <p>Kelola invoice perusahaan</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-6">
                    <div class="info-box">
                        <a href="{{ route('purchase-order.index') }}">
                            <div class="info-content">
                                <div class="info-icon">
                                    <img src="{{ asset('assets/icon/purchase-order.png') }}" alt="Purchase Order">
                                </div>
                                <div class="info-text">
                                    <h3>Purchase Order</h3>
                                    <p>Kelola purchase order</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('footer')
    <div class="pt-4 footer">
        <div class="top-section">
            <div class="left-info">
            </div>
        </div>
        <div class="bottom-nav">
            <div class="flex justify-center items-center gap-4 p-4 rounded-3xl border">
                <div class="btn">
                    <a href="{{route('calender')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-yellow" src="{{ asset('assets/icon/calendar.png') }}"
                                        alt="">
                                </div>
                            </div>
                            <p class="mt-0 text-sm font-semibold text-white pt-2 text-center">Kalender</p>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="{{route('profile')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-purple" src="{{ asset('assets/icon/workplace.png') }}"
                                        alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Profile</div>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="{{route('produk')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-gold" src="{{ asset('assets/icon/newproduct.png') }}"
                                        alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Product</div>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="{{route('informasi')}}">
                    <div class="flex flex-col items-center">
                        <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                            <div class="text-white text-6xl px-2">
                                <img class="imgicon imgicon-green" src="{{ asset('assets/icon/info.png') }}" alt="">
                            </div>
                        </div>
                        <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Informasi</div>
                    </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="{{route('layanan')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-orange" src="{{ asset('assets/icon/support.png') }}" alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Layanan</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        let currentSlide = 0;
        const totalSlides = document.querySelectorAll('.slide').length;
        const sliderTrack = document.getElementById('sliderTrack');
        const dots = document.querySelectorAll('.dot');

        function moveToSlide(index) {
            currentSlide = index;
            const offset = -index * 100;
            sliderTrack.style.transform = `translateX(${offset}%)`;

            dots.forEach(dot => dot.classList.remove('active'));
            dots[index].classList.add('active');
        }

        // Auto slide
        setInterval(() => {
            currentSlide = (currentSlide + 1) % totalSlides;
            moveToSlide(currentSlide);
        }, 5000); // 5 detik

        // Initialize first dot as active
        if (dots.length > 0) {
            dots[0].classList.add('active');
        }
    </script>
@endpush
