@extends('layouts.app')

@section('title', 'PWA Test - PT. Putera Wibowo Borneo')
@section('description', 'Test PWA functionality and features')

@push('vite-assets')
    @vite([
        'resources/js/pwa.js'
    ])
@endpush

@push('inline-styles')
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .test-title {
            color: #0b0643;
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 2rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 1rem;
            margin: 0.5rem 0;
            background: rgba(11, 6, 67, 0.05);
            border-radius: 8px;
            border-left: 4px solid #0b0643;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .status-success {
            background-color: #4CAF50;
        }

        .status-warning {
            background-color: #FF9800;
        }

        .status-error {
            background-color: #F44336;
        }

        .test-button {
            background: #0b0643;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s ease;
        }

        .test-button:hover {
            background: #1a0f5c;
        }

        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            background: #f5f5f5;
            border-left: 4px solid #0b0643;
        }

        .offline-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #F44336;
            color: white;
            border-radius: 25px;
            display: none;
            z-index: 1000;
        }

        .online-indicator {
            background: #4CAF50;
        }
    </style>
@endpush

@section('content')
    <div class="test-container">
        <div class="offline-indicator" id="networkStatus">
            🔴 Offline
        </div>

        <h1 class="test-title">PWA Functionality Test</h1>

        <div class="test-card">
            <h2>PWA Features Status</h2>
            <ul class="feature-list" id="featureList">
                <li>
                    <span class="status-indicator" id="swStatus"></span>
                    <strong>Service Worker:</strong> <span id="swText">Checking...</span>
                </li>
                <li>
                    <span class="status-indicator" id="manifestStatus"></span>
                    <strong>Web App Manifest:</strong> <span id="manifestText">Checking...</span>
                </li>
                <li>
                    <span class="status-indicator" id="httpsStatus"></span>
                    <strong>HTTPS:</strong> <span id="httpsText">Checking...</span>
                </li>
                <li>
                    <span class="status-indicator" id="installStatus"></span>
                    <strong>Installable:</strong> <span id="installText">Checking...</span>
                </li>
                <li>
                    <span class="status-indicator" id="notificationStatus"></span>
                    <strong>Notifications:</strong> <span id="notificationText">Checking...</span>
                </li>
                <li>
                    <span class="status-indicator" id="cacheStatus"></span>
                    <strong>Cache API:</strong> <span id="cacheText">Checking...</span>
                </li>
            </ul>
        </div>

        <div class="test-card">
            <h2>PWA Tests</h2>
            <div>
                <button class="test-button" onclick="testServiceWorker()">Test Service Worker</button>
                <button class="test-button" onclick="testCache()">Test Cache</button>
                <button class="test-button" onclick="testNotification()">Test Notification</button>
                <button class="test-button" onclick="testOffline()">Simulate Offline</button>
                <button class="test-button" onclick="clearCache()">Clear Cache</button>
            </div>
            <div id="testResults" class="test-result" style="display: none;">
                <h3>Test Results:</h3>
                <div id="testOutput"></div>
            </div>
        </div>

        <div class="test-card">
            <h2>App Information</h2>
            <div id="appInfo">
                <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
                <p><strong>Display Mode:</strong> <span id="displayMode"></span></p>
                <p><strong>Viewport:</strong> <span id="viewport"></span></p>
                <p><strong>Connection:</strong> <span id="connection"></span></p>
            </div>
        </div>

        <div class="test-card">
            <h2>Navigation Links</h2>
            <div>
                <a href="/pwa" class="test-button">Home PWA</a>
                <a href="/profile-pwa" class="test-button">Profile PWA</a>
                <a href="/layanan-pwa" class="test-button">Layanan PWA</a>
                <a href="/" class="test-button">Original Home</a>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // PWA Test Functions
        function updateStatus(elementId, status, text) {
            const indicator = document.getElementById(elementId);
            const textElement = document.getElementById(elementId.replace('Status', 'Text'));
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }

        function showTestResult(message) {
            const results = document.getElementById('testResults');
            const output = document.getElementById('testOutput');
            
            output.innerHTML += `<p>${new Date().toLocaleTimeString()}: ${message}</p>`;
            results.style.display = 'block';
        }

        // Check PWA Features
        function checkPWAFeatures() {
            // Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then(registration => {
                    if (registration) {
                        updateStatus('swStatus', 'success', 'Registered and Active');
                    } else {
                        updateStatus('swStatus', 'warning', 'Not Registered');
                    }
                });
            } else {
                updateStatus('swStatus', 'error', 'Not Supported');
            }

            // Manifest
            const manifestLink = document.querySelector('link[rel="manifest"]');
            if (manifestLink) {
                fetch(manifestLink.href)
                    .then(response => response.json())
                    .then(manifest => {
                        updateStatus('manifestStatus', 'success', 'Valid Manifest Found');
                    })
                    .catch(() => {
                        updateStatus('manifestStatus', 'error', 'Invalid Manifest');
                    });
            } else {
                updateStatus('manifestStatus', 'error', 'No Manifest Link');
            }

            // HTTPS
            if (location.protocol === 'https:' || location.hostname === 'localhost') {
                updateStatus('httpsStatus', 'success', 'Secure Connection');
            } else {
                updateStatus('httpsStatus', 'warning', 'HTTP (PWA requires HTTPS in production)');
            }

            // Install Prompt
            if (window.pwaManager && window.pwaManager.deferredPrompt) {
                updateStatus('installStatus', 'success', 'Install Prompt Available');
            } else {
                updateStatus('installStatus', 'warning', 'No Install Prompt (may already be installed)');
            }

            // Notifications
            if ('Notification' in window) {
                updateStatus('notificationStatus', 'success', `Permission: ${Notification.permission}`);
            } else {
                updateStatus('notificationStatus', 'error', 'Not Supported');
            }

            // Cache API
            if ('caches' in window) {
                updateStatus('cacheStatus', 'success', 'Supported');
            } else {
                updateStatus('cacheStatus', 'error', 'Not Supported');
            }
        }

        // Test Functions
        function testServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then(registration => {
                    if (registration) {
                        showTestResult(`✅ Service Worker active. Scope: ${registration.scope}`);
                    } else {
                        showTestResult('❌ No Service Worker registration found');
                    }
                });
            } else {
                showTestResult('❌ Service Worker not supported');
            }
        }

        function testCache() {
            if ('caches' in window) {
                caches.keys().then(cacheNames => {
                    showTestResult(`✅ Found ${cacheNames.length} caches: ${cacheNames.join(', ')}`);
                    
                    cacheNames.forEach(cacheName => {
                        caches.open(cacheName).then(cache => {
                            cache.keys().then(keys => {
                                showTestResult(`📦 Cache "${cacheName}" contains ${keys.length} items`);
                            });
                        });
                    });
                });
            } else {
                showTestResult('❌ Cache API not supported');
            }
        }

        function testNotification() {
            if ('Notification' in window) {
                if (Notification.permission === 'granted') {
                    new Notification('PWA Test', {
                        body: 'Notification test successful!',
                        icon: '/images/icons/icon-192x192.png'
                    });
                    showTestResult('✅ Notification sent');
                } else if (Notification.permission === 'default') {
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            new Notification('PWA Test', {
                                body: 'Notification permission granted!',
                                icon: '/images/icons/icon-192x192.png'
                            });
                            showTestResult('✅ Notification permission granted and sent');
                        } else {
                            showTestResult('❌ Notification permission denied');
                        }
                    });
                } else {
                    showTestResult('❌ Notification permission denied');
                }
            } else {
                showTestResult('❌ Notifications not supported');
            }
        }

        function testOffline() {
            showTestResult('🔄 Testing offline functionality...');
            
            // Try to fetch a resource that should be cached
            fetch('/manifest.json', { cache: 'no-cache' })
                .then(response => {
                    if (response.ok) {
                        showTestResult('✅ Online: Manifest fetched from network');
                    } else {
                        showTestResult('⚠️ Network response not OK');
                    }
                })
                .catch(error => {
                    showTestResult('🔄 Network failed, checking cache...');
                    
                    caches.match('/manifest.json').then(cachedResponse => {
                        if (cachedResponse) {
                            showTestResult('✅ Offline: Manifest served from cache');
                        } else {
                            showTestResult('❌ Offline: No cached version available');
                        }
                    });
                });
        }

        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(cacheNames => {
                    return Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }).then(() => {
                    showTestResult('✅ All caches cleared');
                    location.reload();
                });
            } else {
                showTestResult('❌ Cache API not supported');
            }
        }

        // Update App Info
        function updateAppInfo() {
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('displayMode').textContent = window.matchMedia('(display-mode: standalone)').matches ? 'Standalone' : 'Browser';
            document.getElementById('viewport').textContent = `${window.innerWidth}x${window.innerHeight}`;
            document.getElementById('connection').textContent = navigator.onLine ? 'Online' : 'Offline';
        }

        // Network Status
        function updateNetworkStatus() {
            const indicator = document.getElementById('networkStatus');
            if (navigator.onLine) {
                indicator.textContent = '🟢 Online';
                indicator.className = 'offline-indicator online-indicator';
                indicator.style.display = 'block';
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 3000);
            } else {
                indicator.textContent = '🔴 Offline';
                indicator.className = 'offline-indicator';
                indicator.style.display = 'block';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            checkPWAFeatures();
            updateAppInfo();
            updateNetworkStatus();
        });

        // Network status listeners
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
        window.addEventListener('resize', updateAppInfo);
    </script>
@endpush
