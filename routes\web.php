<?php

use App\Http\Controllers\BestProduct\BestProductController;
use App\Http\Controllers\Calender\Calender;
use App\Http\Controllers\Home\Home;
use App\Http\Controllers\Informasi\Informasi;
use App\Http\Controllers\Invoice\InvoiceController;
use App\Http\Controllers\Layanan\Layanan;
use App\Http\Controllers\Produk\Produk;
use App\Http\Controllers\Profile;
use App\Http\Controllers\PurchaseOrder\PurchaseOrderController;
use Illuminate\Support\Facades\Route;

Route::get('/', [Home::class, 'index'])->name('home');

// PWA Test Routes
Route::get('/pwa', function () {
    return view('home-pwa');
})->name('home.pwa');

Route::get('/profile-pwa', function () {
    return view('profile-pwa');
})->name('profile.pwa');

Route::get('/layanan-pwa', function () {
    return view('layanan-pwa');
})->name('layanan.pwa');

Route::get('/pwa-test', function () {
    return view('pwa-test');
})->name('pwa.test');

// Calender
Route::get('/calender', [Calender::class, 'index'])->name('calender');

// Profile
Route::get('/profile', [Profile::class, 'index'])->name('profile');

// Produk
Route::get('/produk', [Produk::class, 'index'])->name('produk');

// Informasi
Route::get('/informasi', [Informasi::class, 'index'])->name('informasi');

// Layanan
Route::get('/layanan', [Layanan::class, 'index'])->name('layanan');

// Invoice
Route::resource('invoice', InvoiceController::class);

// Purchase Order
Route::resource('purchase-order', PurchaseOrderController::class);

// Best Product
Route::resource('best_product', BestProductController::class);