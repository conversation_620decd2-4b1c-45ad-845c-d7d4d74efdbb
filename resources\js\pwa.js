// PWA functionality and utilities

class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isOnline = navigator.onLine;
        
        this.init();
    }

    init() {
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupNetworkStatus();
        this.setupNotifications();
        this.checkInstallStatus();
    }

    // Service Worker Registration
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('PWA: Service Worker registered successfully', registration);
                
                // Handle service worker updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateNotification();
                        }
                    });
                });
                
            } catch (error) {
                console.error('PWA: Service Worker registration failed', error);
            }
        }
    }

    // Install Prompt Management
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });

        window.addEventListener('appinstalled', () => {
            console.log('PWA: App installed successfully');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showInstallSuccessMessage();
        });
    }

    // Network Status Management
    setupNetworkStatus() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showNetworkStatus('online');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showNetworkStatus('offline');
        });
    }

    // Notification Setup
    setupNotifications() {
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                this.requestNotificationPermission();
            }
        }
    }

    // Check if app is already installed
    checkInstallStatus() {
        // Check if running in standalone mode
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            this.isInstalled = true;
            this.hideInstallButton();
        }
    }

    // Show install button
    showInstallButton() {
        const installBtn = document.getElementById('pwa-install-btn');
        if (installBtn && !this.isInstalled) {
            installBtn.classList.add('show');
            installBtn.addEventListener('click', () => this.installApp());
        }
    }

    // Hide install button
    hideInstallButton() {
        const installBtn = document.getElementById('pwa-install-btn');
        if (installBtn) {
            installBtn.classList.remove('show');
        }
    }

    // Install the app
    async installApp() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            console.log(`PWA: Install prompt outcome: ${outcome}`);
            
            if (outcome === 'accepted') {
                this.hideInstallButton();
            }
            
            this.deferredPrompt = null;
        }
    }

    // Show network status
    showNetworkStatus(status) {
        const statusElement = this.createNetworkStatusElement();
        statusElement.className = `network-status ${status}`;
        statusElement.textContent = status === 'online' ? 
            '🟢 Kembali online' : '🔴 Sedang offline';
        
        document.body.appendChild(statusElement);
        
        setTimeout(() => {
            statusElement.remove();
        }, 3000);
    }

    // Create network status element
    createNetworkStatusElement() {
        const element = document.createElement('div');
        element.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 10000;
            transition: all 0.3s ease;
        `;
        return element;
    }

    // Show update notification
    showUpdateNotification() {
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div style="
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: #0b0643;
                color: white;
                padding: 15px;
                border-radius: 10px;
                z-index: 10000;
                display: flex;
                justify-content: space-between;
                align-items: center;
            ">
                <span>Update tersedia! Refresh untuk mendapatkan versi terbaru.</span>
                <button onclick="window.location.reload()" style="
                    background: white;
                    color: #0b0643;
                    border: none;
                    padding: 5px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                ">Refresh</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 10000);
    }

    // Show install success message
    showInstallSuccessMessage() {
        const message = this.createNetworkStatusElement();
        message.textContent = '✅ Aplikasi berhasil diinstall!';
        message.style.background = '#4CAF50';
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    // Request notification permission
    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();
            console.log('PWA: Notification permission:', permission);
            return permission === 'granted';
        } catch (error) {
            console.error('PWA: Error requesting notification permission', error);
            return false;
        }
    }

    // Show notification
    showNotification(title, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                icon: '/images/icons/icon-192x192.png',
                badge: '/images/icons/icon-72x72.png',
                ...options
            });
            
            notification.onclick = () => {
                window.focus();
                notification.close();
            };
            
            return notification;
        }
    }

    // Sync offline data
    async syncOfflineData() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            try {
                const registration = await navigator.serviceWorker.ready;
                await registration.sync.register('background-sync');
                console.log('PWA: Background sync registered');
            } catch (error) {
                console.error('PWA: Background sync registration failed', error);
            }
        }
    }

    // Add to home screen prompt for iOS
    showIOSInstallPrompt() {
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isInStandaloneMode = window.navigator.standalone;
        
        if (isIOS && !isInStandaloneMode) {
            const prompt = document.createElement('div');
            prompt.innerHTML = `
                <div style="
                    position: fixed;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    background: #0b0643;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    z-index: 10000;
                ">
                    <p>Install aplikasi ini di iPhone Anda: tap <strong>Share</strong> dan kemudian <strong>Add to Home Screen</strong>.</p>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: white;
                        color: #0b0643;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        margin-top: 10px;
                        cursor: pointer;
                    ">Tutup</button>
                </div>
            `;
            
            document.body.appendChild(prompt);
        }
    }

    // Get app info
    getAppInfo() {
        return {
            isInstalled: this.isInstalled,
            isOnline: this.isOnline,
            hasServiceWorker: 'serviceWorker' in navigator,
            hasNotifications: 'Notification' in window,
            notificationPermission: 'Notification' in window ? Notification.permission : 'not-supported'
        };
    }
}

// Initialize PWA Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAManager();
    
    // Show iOS install prompt after 3 seconds
    setTimeout(() => {
        window.pwaManager.showIOSInstallPrompt();
    }, 3000);
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAManager;
}
