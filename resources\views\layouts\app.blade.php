<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="@yield('description', 'PT. Putera Wibowo Borneo - Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri')" />
    <meta name="keywords" content="alat berat, teknologi industri, PT Putera Wibowo Borneo, PWB" />
    <meta name="author" content="PT. Putera Wibowo Borneo" />
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0b0643" />
    <meta name="msapplication-TileColor" content="#0b0643" />
    <meta name="msapplication-TileImage" content="{{ asset('images/icons/icon-144x144.png') }}" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="PWB App" />
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="{{ asset('images/icons/icon-192x192.png') }}" />
    <link rel="apple-touch-icon" sizes="72x72" href="{{ asset('images/icons/icon-72x72.png') }}" />
    <link rel="apple-touch-icon" sizes="96x96" href="{{ asset('images/icons/icon-96x96.png') }}" />
    <link rel="apple-touch-icon" sizes="144x144" href="{{ asset('images/icons/icon-144x144.png') }}" />
    <link rel="apple-touch-icon" sizes="192x192" href="{{ asset('images/icons/icon-192x192.png') }}" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/icons/icon-48x48.png') }}" />
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/icons/icon-48x48.png') }}" />
    <link rel="shortcut icon" href="{{ asset('images/loogo.ico') }}" />
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="{{ asset('manifest.json') }}" />
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <title>@yield('title', 'PWB App - PT. Putera Wibowo Borneo')</title>
    
    <!-- Vite Assets -->
    @vite([
        'resources/css/app.css',
        'resources/css/lazy-loading.css',
        'resources/js/app.js',
        'resources/js/lazy-loading.js'
    ])
    
    <!-- Additional CSS -->
    @stack('styles')
    
    <!-- Additional Vite Assets -->
    @stack('vite-assets')
    
    <style>
        * {
            font-family: Helvetica, sans-serif !important;
            color: #0b0643;
        }

        html {
            background: none;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-image:
                linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
                url('{{ asset('images/bg.png') }}');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            overflow-x: hidden;
        }

        .header {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
        }

        .main-content {
            flex: 1;
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
            padding: 0;
        }

        .footer {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
        }

        /* Responsive adjustments for larger screens */
        @media (min-width: 1025px) {
            .main-content {
                padding: clamp(0.5rem, 1vw, 1rem) 0;
            }
        }
        
        /* PWA Install Button */
        .pwa-install-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #0b0643;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(11, 6, 67, 0.3);
            z-index: 1000;
            display: none;
            transition: all 0.3s ease;
        }
        
        .pwa-install-button:hover {
            background: #1a0f5c;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(11, 6, 67, 0.4);
        }
        
        .pwa-install-button.show {
            display: block;
        }
    </style>
    
    <!-- Additional inline styles -->
    @stack('inline-styles')
</head>

<body>
    <!-- PWA Install Button -->
    <button id="pwa-install-btn" class="pwa-install-button">
        📱 Install App
    </button>
    
    <!-- Header Section -->
    @hasSection('header')
        @yield('header')
    @else
        <div class="div1 header">
            <div class="right-logo">
                <div class="logo-line">
                    <img src="{{ asset('images/logo.png') }}" alt="Logo" />
                    <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
                </div>
                <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"</div>
            </div>
        </div>
    @endif

    <!-- Main Content -->
    <div class="main-content">
        @yield('content')
    </div>

    <!-- Footer Section -->
    @hasSection('footer')
        @yield('footer')
    @else
        <div class="pt-4 footer">
            <div class="top-section">
                <div class="left-info">
                </div>
            </div>
            <div class="bottom-nav">
                <div class="flex justify-center items-center gap-4 p-4 rounded-3xl border">
                    <div class="btn">
                        <a href="{{route('calender')}}">
                            <div class="flex flex-col items-center">
                                <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                    <div class="text-white text-6xl px-2">
                                        <img class="imgicon imgicon-yellow" src="{{ asset('assets/icon/calendar.png') }}" alt="">
                                    </div>
                                </div>
                                <p class="mt-0 text-sm font-semibold text-white pt-2 text-center">Kalender</p>
                            </div>
                        </a>
                    </div>
                    <div class="btn">
                        <a href="{{route('profile')}}">
                            <div class="flex flex-col items-center">
                                <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                    <div class="text-white text-6xl px-2">
                                        <img class="imgicon imgicon-yellow" src="{{ asset('assets/icon/profile.png') }}" alt="">
                                    </div>
                                </div>
                                <p class="mt-0 text-sm font-semibold text-white pt-2 text-center">Profile</p>
                            </div>
                        </a>
                    </div>
                    <div class="btn">
                        <a href="{{route('layanan')}}">
                            <div class="flex flex-col items-center">
                                <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                    <div class="text-white text-6xl px-2">
                                        <img class="imgicon imgicon-yellow" src="{{ asset('assets/icon/layanan.png') }}" alt="">
                                    </div>
                                </div>
                                <p class="mt-0 text-sm font-semibold text-white pt-2 text-center">Layanan</p>
                            </div>
                        </a>
                    </div>
                    <div class="btn">
                        <a href="{{route('produk')}}">
                            <div class="flex flex-col items-center">
                                <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                    <div class="text-white text-6xl px-2">
                                        <img class="imgicon imgicon-yellow" src="{{ asset('assets/icon/produk.png') }}" alt="">
                                    </div>
                                </div>
                                <p class="mt-0 text-sm font-semibold text-white pt-2 text-center">Produk</p>
                            </div>
                        </a>
                    </div>
                    <div class="btn">
                        <a href="{{route('informasi')}}">
                            <div class="flex flex-col items-center">
                                <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                    <div class="text-white text-6xl px-2">
                                        <img class="imgicon imgicon-yellow" src="{{ asset('assets/icon/informasi.png') }}" alt="">
                                    </div>
                                </div>
                                <p class="mt-0 text-sm font-semibold text-white pt-2 text-center">Informasi</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Additional JavaScript -->
    @stack('scripts')
    
    <!-- PWA Service Worker Registration -->
    <script>
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        const installButton = document.getElementById('pwa-install-btn');

        window.addEventListener('beforeinstallprompt', (e) => {
            // Prevent the mini-infobar from appearing on mobile
            e.preventDefault();
            // Stash the event so it can be triggered later
            deferredPrompt = e;
            // Show the install button
            installButton.classList.add('show');
        });

        installButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                // Show the install prompt
                deferredPrompt.prompt();
                // Wait for the user to respond to the prompt
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                // Clear the deferredPrompt variable
                deferredPrompt = null;
                // Hide the install button
                installButton.classList.remove('show');
            }
        });

        // Hide install button if app is already installed
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            installButton.classList.remove('show');
            deferredPrompt = null;
        });

        // Check if app is running in standalone mode
        if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone) {
            console.log('App is running in standalone mode');
            installButton.style.display = 'none';
        }
    </script>
</body>

</html>
